<script setup lang="ts">
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// 直接打开GoView编辑器
const openGoViewEditor = () => {
  if (!authStore.isLoggedIn) {
    console.warn('用户未登录，无法打开GoView编辑器')
    return
  }

  // 构建带完整认证参数的GoView URL
  const token = authStore.token
  const refreshToken = authStore.refreshToken
  const expiresIn = authStore.expiresIn
  const user = authStore.user

  if (!token || !refreshToken || !user) {
    console.error('无法获取完整认证信息')
    return
  }

  // 构建完整认证参数
  const authParams = new URLSearchParams({
    ccapi_token: token,
    ccapi_refresh_token: refreshToken,
    ccapi_expires_in: String(expiresIn),
    ccapi_user: user.username,
    ccapi_nickname: user.nickname || user.username,
    ccapi_user_id: String(user.id || 0),
    skip_login: 'true',
    source: 'ccapi'
  })

  // 直接打开GoView的项目列表页面
  const baseUrl = 'http://localhost:3001'
  const hashRoute = '#/project/items'
  const goviewUrl = `${baseUrl}${hashRoute}?${authParams.toString()}`

  console.log('🔍 直接打开GoView编辑器:', {
    isLoggedIn: authStore.isLoggedIn,
    token: token ? token.substring(0, 10) + '...' : 'null',
    refreshToken: refreshToken ? refreshToken.substring(0, 10) + '...' : 'null',
    expiresIn,
    user: user?.username,
    finalUrl: goviewUrl
  })

  // 打开新标签页显示GoView
  const newTab = window.open(goviewUrl, '_blank')

  if (newTab) {
    newTab.focus()
  } else {
    console.error('无法打开新标签页，可能被浏览器阻止')
    alert('请允许弹窗，然后重试')
  }
}
</script>

<template>
  <div class="container mx-auto p-6">
    <div class="hero min-h-[60vh] bg-base-200 rounded-lg">
      <div class="hero-content text-center">
        <div class="max-w-md">
          <h1 class="text-5xl font-bold">CCAPI 管理系统</h1>
          <p class="py-6">
            欢迎使用 CCAPI 后台管理系统。您可以在这里管理订单数据和任务执行实例。
          </p>
          <div class="flex gap-4 justify-center flex-wrap">
            <router-link to="/orders" class="btn btn-primary">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              订单管理
            </router-link>

            <button @click="openGoViewEditor" class="btn btn-secondary">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              GoView 数据可视化
            </button>

            <router-link to="/lowcode" class="btn btn-accent">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
              </svg>
              低代码配置
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-8">
      <div class="stat bg-base-100 shadow rounded-lg">
        <div class="stat-figure text-primary">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <div class="stat-title">订单管理</div>
        <div class="stat-value text-primary">订单</div>
        <div class="stat-desc">查询、搜索、导出订单数据</div>
      </div>



      <div class="stat bg-base-100 shadow rounded-lg">
        <div class="stat-figure text-accent">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div class="stat-title">数据导出</div>
        <div class="stat-value text-accent">导出</div>
        <div class="stat-desc">支持Excel、PDF、CSV格式</div>
      </div>

      <div class="stat bg-base-100 shadow rounded-lg">
        <div class="stat-figure text-info">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        </div>
        <div class="stat-title">用户</div>
        <div class="stat-value text-info">{{ authStore.user?.username || '未知' }}</div>
        <div class="stat-desc">当前登录用户</div>
      </div>
    </div>
  </div>
</template>
