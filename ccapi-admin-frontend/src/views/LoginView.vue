<template>
  <div class="min-h-screen bg-gradient-to-br from-primary/10 via-base-200 to-secondary/10 flex items-center justify-center p-4">
    <div class="w-full max-w-md">
      <!-- 登录卡片 -->
      <div class="card bg-base-100 shadow-2xl">
        <div class="card-body p-8">
          <!-- Logo和标题 -->
          <div class="text-center mb-8">
            <div class="flex justify-center mb-4">
              <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center">
                <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
            </div>
            <h1 class="text-2xl font-bold text-base-content">CCAPI 管理系统</h1>
            <p class="text-base-content/60 mt-2">请登录您的账户</p>
          </div>



          <!-- 错误提示 -->
          <div v-if="errorMessage" class="alert alert-error mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{{ errorMessage }}</span>
          </div>

          <!-- 登录表单 -->
          <form @submit.prevent="handleLogin" class="space-y-4">
            <!-- 用户名 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">用户名</span>
              </label>
              <input
                v-model="loginForm.username"
                type="text"
                placeholder="请输入用户名"
                class="input input-bordered w-full"
                :class="{ 'input-error': errors.username }"
                required
              />
              <label v-if="errors.username" class="label">
                <span class="label-text-alt text-error">{{ errors.username }}</span>
              </label>
            </div>

            <!-- 密码 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">密码</span>
              </label>
              <input
                v-model="rawPassword"
                type="password"
                placeholder="请输入密码"
                class="input input-bordered w-full"
                :class="{ 'input-error': errors.password }"
                required
              />
              <label v-if="errors.password" class="label">
                <span class="label-text-alt text-error">{{ errors.password }}</span>
              </label>
            </div>

            <!-- 验证码 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">验证码</span>
              </label>
              <div class="flex gap-2">
                <input
                  v-model="loginForm.captcha"
                  type="text"
                  placeholder="请输入验证码"
                  class="input input-bordered flex-1"
                  :class="{ 'input-error': errors.captcha }"
                  required
                />
                <div class="relative">
                  <img
                    v-if="captchaImage"
                    :src="captchaImage"
                    alt="验证码"
                    class="h-12 w-32 border border-base-300 rounded cursor-pointer hover:opacity-80 transition-opacity"
                    @click="refreshCaptcha"
                    @error="handleImageError"
                    @load="handleImageLoad"
                  />
                  <div v-else class="h-12 w-32 border border-base-300 rounded flex items-center justify-center bg-base-200">
                    <span class="text-xs text-base-content/40">加载中...</span>
                  </div>
                  <!-- 刷新按钮 -->
                  <button
                    type="button"
                    class="absolute -top-1 -right-1 btn btn-circle btn-xs btn-ghost bg-base-100 border border-base-300"
                    @click="refreshCaptcha"
                    title="刷新验证码"
                  >
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                  </button>
                </div>
              </div>
              <label v-if="errors.captcha" class="label">
                <span class="label-text-alt text-error">{{ errors.captcha }}</span>
              </label>
              <label class="label">
                <span class="label-text-alt text-base-content/60">点击图片可刷新验证码</span>
              </label>
            </div>

            <!-- 登录按钮 -->
            <div class="form-control mt-6">
              <button
                type="submit"
                class="btn btn-primary w-full"
                :disabled="isLoading"
              >
                <span v-if="!isLoading">登录</span>
                <span v-else class="flex items-center gap-2">
                  <span class="loading loading-bars loading-sm"></span>
                  登录中...
                </span>
              </button>


            </div>
          </form>
        </div>
      </div>


    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import * as authApi from '@/api/auth'
import { encryptPassword } from '@/utils/crypto'
import { toast } from '@/utils/toast'
import type { LoginRequest } from '@/types'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const isLoading = ref(false)
const loadingMessage = ref('')
const errorMessage = ref('')

const captchaImage = ref('')
const captchaId = ref('')
const rawPassword = ref('Pix9523cc') // 原始密码，用于显示

// 登录表单数据
const loginForm = reactive<LoginRequest>({
  username: 'admin',
  password: '',  // 这里会在watch中自动设置为MD5加密后的密码
  captcha: '',
  captchaId: ''
})

// 表单验证错误
const errors = reactive({
  username: '',
  password: '',
  captcha: ''
})

// 监听原始密码变化，自动进行MD5加密
watch(rawPassword, (newPassword) => {
  loginForm.password = encryptPassword(newPassword)
}, { immediate: true })

// 清除错误信息
const clearErrors = () => {
  errorMessage.value = ''
  loadingMessage.value = ''
  errors.username = ''
  errors.password = ''
  errors.captcha = ''
}

// 表单验证
const validateForm = () => {
  clearErrors()
  let isValid = true

  if (!loginForm.username.trim()) {
    errors.username = '请输入用户名'
    isValid = false
  }

  if (!rawPassword.value.trim()) {
    errors.password = '请输入密码'
    isValid = false
  }

  if (!loginForm.captcha.trim()) {
    errors.captcha = '请输入验证码'
    isValid = false
  }

  return isValid
}

// 获取验证码
const getCaptcha = async () => {
  try {
    console.log('开始获取验证码...')
    const response = await authApi.getCaptcha()
    console.log('验证码API响应:', response)
    
    captchaImage.value = response.image
    captchaId.value = response.captchaId
    loginForm.captchaId = response.captchaId
    console.log('获取验证码成功:', {
      captchaId: response.captchaId,
      imageLength: response.image.length,
      imagePrefix: response.image.substring(0, 50)
    })
  } catch (error) {
    console.error('获取验证码失败:', error)
    errorMessage.value = '获取验证码失败，请刷新页面重试'
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  loginForm.captcha = ''
  getCaptcha()
}

// 图片加载错误处理
const handleImageError = (event: Event) => {
  console.error('验证码图片加载失败:', event)
  errorMessage.value = '验证码图片加载失败，请点击刷新'
}

// 图片加载成功处理
const handleImageLoad = (event: Event) => {
  console.log('验证码图片加载成功')
}

// 处理登录
const handleLogin = async () => {
  if (!validateForm()) {
    return
  }

  if (!captchaId.value) {
    errorMessage.value = '请先获取验证码'
    return
  }

  isLoading.value = true
  clearErrors()

  try {
    console.log('开始登录流程:', {
      username: loginForm.username,
      captchaId: loginForm.captchaId,
      captcha: loginForm.captcha,
      passwordMD5: loginForm.password
    })

    // 第一步：验证验证码
    loadingMessage.value = '正在验证验证码...'
    console.log('第一步：验证验证码')
    const captchaVerifyResult = await authApi.verifyCaptcha({
      captchaId: loginForm.captchaId,
      captchaCode: loginForm.captcha
    })

    console.log('验证码验证结果:', captchaVerifyResult)

    if (!captchaVerifyResult.success) {
      errorMessage.value = captchaVerifyResult.message || '验证码验证失败'
      refreshCaptcha()
      return
    }

    // 第二步：执行登录
    loadingMessage.value = '正在验证用户信息...'
    console.log('第二步：执行登录')
    const response = await authStore.login(loginForm)

    if (response.success) {
      console.log('登录成功')

      // 更新加载消息
      loadingMessage.value = '登录成功！正在跳转...'



      // 显示Toast通知
      toast.loginSuccess(loginForm.username)

      // 延迟跳转，让用户看到Toast通知
      setTimeout(() => {
        const redirect = router.currentRoute.value.query.redirect as string

        // 检查是否是从 goview 跳转来的
        if (redirect && redirect.includes('localhost:3001')) {
          console.log('检测到从 GoView 跳转来的登录请求，准备带 token 跳转回去')

          // 从 goview 跳转来的，需要带 token 跳转回去
          const token = authStore.token
          const user = authStore.user

          if (token && user) {
            try {
              const redirectUrl = new URL(redirect)
              redirectUrl.searchParams.set('ccapi_token', token)
              redirectUrl.searchParams.set('ccapi_user', user.username)
              redirectUrl.searchParams.set('ccapi_nickname', user.nickname || user.username)
              redirectUrl.searchParams.set('ccapi_user_id', user.id?.toString() || '1')

              console.log('跳转回 GoView:', redirectUrl.toString())
              window.location.href = redirectUrl.toString()
              return
            } catch (error) {
              console.error('构造回跳 URL 失败:', error)
            }
          }
        }

        // 普通跳转逻辑
        router.push(redirect || '/')
      }, 1500) // 1.5秒后跳转，给用户足够时间看到Toast
    } else {
      errorMessage.value = '登录失败'
      refreshCaptcha()
    }
  } catch (error: any) {
    console.error('登录失败:', error)
    errorMessage.value = error.message || '登录失败，请检查网络连接'
    refreshCaptcha()
  } finally {
    isLoading.value = false
  }
}

// 组件挂载时获取验证码
onMounted(() => {
  getCaptcha()
})
</script>

<style scoped>








/* 增强现有的动画 */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* 成功提示的特殊动画 */
.alert-success {
  animation: successPulse 0.8s ease-out;
}

@keyframes successPulse {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
