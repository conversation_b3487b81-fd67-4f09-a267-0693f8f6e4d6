# go-view 登录系统统一改造总结

## 改造目标
废弃 go-view 自己的登录页面，统一使用 CCAPI 的登录入口，实现真正的单点登录(SSO)。

## 主要修改内容

### 1. 前端路由清理
- ✅ 移除登录页面路由 (`src/router/base.ts`)
- ✅ 更新路由配置 (`src/router/index.ts`)
- ✅ 修改路由守卫白名单 (`src/router/router-guards.ts`)
- ✅ 删除登录页面组件 (`src/views/login/index.vue`)

### 2. API 接口清理
- ✅ 移除登录相关 API (`src/api/path/system.api.ts`)
- ✅ 清理接口白名单 (`src/api/axios.config.ts`)
- ✅ 删除登录相关类型定义 (`src/api/path/system.d.ts`)

### 3. 后端接口清理
- ✅ 移除登录路由 (`controllers/goview.go`)
- ✅ 删除 Login 和 Logout 方法
- ✅ 清理中间件白名单 (`routers/middleware.go`)
- ✅ 删除登录相关结构体

### 4. 认证流程统一
- ✅ 修改 axios 响应拦截器，支持业务状态码 401
- ✅ 统一跳转到 CCAPI 登录页面
- ✅ 修改路由守卫，无认证时跳转到 CCAPI
- ✅ 修改错误页面的登录跳转逻辑

### 5. 国际化文件清理
- ✅ 删除登录相关国际化文件
- ✅ 更新国际化配置

### 6. 工具函数更新
- ✅ 修改 logout 函数，跳转到 CCAPI 登录页面
- ✅ 清理登录相关引用

## 认证流程

### 用户访问 go-view
1. **无认证状态**：直接跳转到 CCAPI 登录页面
2. **CCAPI 登录成功**：带 token 跳转回 go-view
3. **go-view 接收 token**：自动设置登录状态
4. **后续请求**：自动携带 X-Token 头

### Token 失效处理
1. **业务状态码 401**：跳转到 CCAPI 登录页面
2. **HTTP 状态码 401**：跳转到 CCAPI 登录页面
3. **路由守卫检查**：无 token 时跳转到 CCAPI 登录页面

## 保留的功能
- ✅ 预览功能（无需认证）
- ✅ CCAPI token 自动接收和设置
- ✅ 用户信息显示和管理

## 技术细节

### 跳转 URL 格式
```
http://localhost:3000/lowcode/login?redirect=${encodeURIComponent(currentUrl)}
```

### Token 传递方式
- **请求头**：`X-Token`
- **存储位置**：`localStorage`
- **用户信息**：SystemStore

### 白名单接口
- `/api/goview/project/getData` - 预览功能

## 测试验证
1. ✅ 访问 go-view 首页自动跳转到 CCAPI 登录
2. ✅ CCAPI 登录成功后自动跳转回 go-view
3. ✅ go-view 接口调用携带正确的 X-Token
4. ✅ Token 失效时自动跳转到 CCAPI 登录
5. ✅ 预览功能正常工作（无需认证）

## 安全改进
- ✅ 所有需要认证的接口都进行 token 校验
- ✅ 统一的认证入口，避免多套认证系统
- ✅ 自动处理 token 失效和刷新
- ✅ 清理了不安全的本地登录逻辑

## 后续维护
- 所有认证相关的修改都在 CCAPI 系统中进行
- go-view 只负责接收和使用 CCAPI 提供的认证信息
- 保持两个系统的认证机制同步
