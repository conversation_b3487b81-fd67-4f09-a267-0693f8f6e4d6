import { Router } from 'vue-router';
import { PageEnum, PreviewEnum } from '@/enums/pageEnum'
import { loginCheck } from '@/utils'
import { useSystemStore } from '@/store/modules/systemStore/systemStore'
import { SystemStoreEnum, SystemStoreUserInfoEnum } from '@/store/modules/systemStore/systemStore.d'

// 路由白名单
const routerAllowList = [
  // 登录
  PageEnum.BASE_LOGIN_NAME,
  // 预览
  PreviewEnum.CHART_PREVIEW_NAME
]

// 检查CCAPI token并自动登录（参考CCAPI前端的实现方案）
const checkCCAPIToken = (query: any) => {
  const ccapiToken = query.ccapi_token
  const ccapiUser = query.ccapi_user
  const ccapiNickname = query.ccapi_nickname
  const ccapiUserId = query.ccapi_user_id

  console.log('🔍 GoView路由守卫检查:', {
    query,
    ccapiToken: ccapiToken ? ccapiToken.substring(0, 10) + '...' : 'null',
    ccapiUser,
    hasToken: !!ccapiToken,
    hasUser: !!ccapiUser
  })

  if (ccapiToken && ccapiUser) {
    console.log('✅ 检测到CCAPI token，直接设置登录状态...')

    // 参考CCAPI的做法：有token就认为有效，直接设置登录状态
    // 如果token无效，后续API调用会返回401，axios拦截器会处理
    const systemStore = useSystemStore()

    // 设置用户信息，确保与CCAPI认证机制完全一致
    systemStore.setItem(SystemStoreEnum.USER_INFO, {
      [SystemStoreUserInfoEnum.USER_TOKEN]: ccapiToken,
      [SystemStoreUserInfoEnum.TOKEN_NAME]: 'X-Token', // 与CCAPI前端保持一致
      [SystemStoreUserInfoEnum.USER_ID]: ccapiUserId || '1',
      [SystemStoreUserInfoEnum.USER_NAME]: ccapiUser,
      [SystemStoreUserInfoEnum.NICK_NAME]: ccapiNickname || ccapiUser,
    })

    console.log('✅ 已设置GoView用户信息:', {
      token: ccapiToken.substring(0, 10) + '...',
      tokenName: 'X-Token',
      userId: ccapiUserId,
      userName: ccapiUser
    })

    console.log('CCAPI自动登录成功')
    return true
  }

  return false
}

export function createRouterGuards(router: Router) {
  // 前置
  router.beforeEach(async (to, from, next) => {
    // http://localhost:3000/#/chart/preview/792622755697790976?t=123
    // 把外部动态参数放入window.route.params，后续API动态接口可以用window.route?.params?.t来拼接参数
    // @ts-ignore
    if (!window.route) window.route = {params: {}}
    // @ts-ignore
    Object.assign(window.route.params, to.query)

    const Loading = window['$loading'];
    Loading && Loading.start();
    const isErrorPage = router.getRoutes().findIndex((item) => item.name === to.name);
    if (isErrorPage === -1) {
      next({ name: PageEnum.ERROR_PAGE_NAME_404 })
    }

    // 检查是否需要登录（参考CCAPI的路由守卫逻辑）
    // @ts-ignore
    if (!routerAllowList.includes(to.name)) {
      // 先检查CCAPI token
      const ccapiLoginSuccess = checkCCAPIToken(to.query)

      // 如果CCAPI登录成功或者已经登录，则继续
      if (ccapiLoginSuccess || loginCheck()) {
        next()
        return
      }

      // 否则跳转到 CCAPI 登录页面，传递当前页面作为回跳地址
      const currentUrl = window.location.href
      window.location.href = `http://localhost:3000/lowcode/login?redirect=${encodeURIComponent(currentUrl)}`
      return
    }

    // 白名单路由直接通过
    next()
  })

  router.afterEach((to, _, failure) => {
    const Loading = window['$loading'];
    document.title = (to?.meta?.title as string) || document.title;
    Loading && Loading.finish();
  })

  // 错误
  router.onError((error) => {
    console.log(error, '路由错误');
  });
}