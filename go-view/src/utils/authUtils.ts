/**
 * 认证相关工具函数
 */

// 需要清理的认证参数列表
const AUTH_PARAMS = [
  'external_token',
  'external_refresh_token', 
  'external_expires_in',
  'external_user',
  'external_nickname',
  'external_user_id',
  'external_system',
  'external_login_url',
  // 保持对旧格式的清理支持
  'ccapi_token',
  'ccapi_refresh_token',
  'ccapi_expires_in',
  'ccapi_user',
  'ccapi_nickname',
  'ccapi_user_id'
]

/**
 * 清理URL中的认证参数
 * @param url 原始URL
 * @returns 清理后的URL
 */
export function cleanAuthParams(url: string): string {
  try {
    const urlObj = new URL(url)
    
    // 移除所有认证相关参数
    AUTH_PARAMS.forEach(param => {
      urlObj.searchParams.delete(param)
    })
    
    return urlObj.toString()
  } catch (error) {
    console.error('清理URL参数失败:', error)
    return url
  }
}

/**
 * 构造登录跳转URL
 * @param loginUrl 登录页面URL
 * @param redirectUrl 登录成功后的跳转URL
 * @returns 完整的登录跳转URL
 */
export function buildLoginUrl(loginUrl: string, redirectUrl: string): string {
  try {
    const loginUrlObj = new URL(loginUrl)
    
    // 清理跳转URL中的认证参数
    const cleanRedirectUrl = cleanAuthParams(redirectUrl)
    
    // 添加redirect参数
    loginUrlObj.searchParams.set('redirect', cleanRedirectUrl)
    
    return loginUrlObj.toString()
  } catch (error) {
    console.error('构造登录URL失败:', error)
    return loginUrl
  }
}

/**
 * 执行登录跳转
 * @param loginUrl 登录页面URL，如果为空则使用默认URL
 * @param currentUrl 当前页面URL，默认使用window.location.href
 */
export function redirectToLogin(loginUrl?: string, currentUrl?: string): void {
  const defaultLoginUrl = 'http://localhost:3000/lowcode/login'
  const finalLoginUrl = loginUrl || defaultLoginUrl
  const finalCurrentUrl = currentUrl || window.location.href
  
  const redirectUrl = buildLoginUrl(finalLoginUrl, finalCurrentUrl)
  
  console.log('🔄 跳转到登录页面:', {
    loginUrl: finalLoginUrl,
    currentUrl: finalCurrentUrl,
    finalUrl: redirectUrl
  })
  
  window.location.href = redirectUrl
}

/**
 * 验证必需的认证参数
 * @param params URL参数对象
 * @returns 验证结果和错误信息
 */
export function validateAuthParams(params: any): { valid: boolean; error?: string } {
  const requiredParams = ['external_token', 'external_refresh_token', 'external_user']
  
  for (const param of requiredParams) {
    if (!params[param]) {
      return {
        valid: false,
        error: `缺少必需参数: ${param}`
      }
    }
  }
  
  return { valid: true }
}

/**
 * 解析外部认证参数
 * @param query 路由查询参数
 * @returns 解析后的认证信息
 */
export function parseExternalAuthParams(query: any) {
  return {
    token: query.external_token,
    refreshToken: query.external_refresh_token,
    expiresIn: query.external_expires_in,
    user: query.external_user,
    nickname: query.external_nickname,
    userId: query.external_user_id,
    sourceSystem: query.external_system || 'unknown',
    loginUrl: query.external_login_url
  }
}
