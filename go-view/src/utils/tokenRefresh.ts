import { useSystemStore } from '@/store/modules/systemStore/systemStore'
import { SystemStoreEnum, SystemStoreUserInfoEnum } from '@/store/modules/systemStore/systemStore.d'
import axios from 'axios'

class TokenRefreshManager {
  private refreshTimer: NodeJS.Timeout | null = null
  private isRefreshing = false
  private refreshPromise: Promise<string> | null = null

  // 初始化token刷新机制
  init() {
    console.log('🔄 初始化Token刷新机制...')
    this.scheduleTokenRefresh()
    console.log('✅ Token刷新机制已启动')
  }

  // 计算token刷新时间
  private getRefreshTime(): number {
    const systemStore = useSystemStore()
    const userInfo = systemStore.userInfo
    const expiresIn = userInfo?.[SystemStoreUserInfoEnum.EXPIRES_IN]
    
    if (!expiresIn) {
      console.warn('⚠️ 未找到token过期时间，无法安排自动刷新')
      return 0
    }
    
    const now = Math.floor(Date.now() / 1000)
    const expiresAt = parseInt(expiresIn)
    const timeUntilExpiry = expiresAt - now
    
    // 提前5分钟刷新token，最少1分钟后刷新
    const refreshTime = Math.max(timeUntilExpiry - 300, 60)
    
    console.log('🕐 Token刷新计划:', {
      now,
      expiresAt,
      timeUntilExpiry: `${timeUntilExpiry}秒`,
      refreshTime: `${refreshTime}秒后刷新`
    })
    
    return refreshTime * 1000 // 转换为毫秒
  }

  // 安排token刷新
  private scheduleTokenRefresh() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
    }

    const refreshTime = this.getRefreshTime()
    if (refreshTime <= 0) return

    this.refreshTimer = setTimeout(() => {
      console.log('⏰ 定时器触发，开始刷新token...')
      this.refreshToken()
    }, refreshTime)
  }

  // 刷新token
  public async refreshToken(): Promise<string> {
    if (this.isRefreshing && this.refreshPromise) {
      console.log('🔄 Token刷新已在进行中，等待结果...')
      return this.refreshPromise
    }

    this.isRefreshing = true
    this.refreshPromise = this.doRefreshToken()

    try {
      const newToken = await this.refreshPromise
      this.scheduleTokenRefresh() // 安排下次刷新
      return newToken
    } finally {
      this.isRefreshing = false
      this.refreshPromise = null
    }
  }

  // 执行token刷新
  private async doRefreshToken(): Promise<string> {
    const systemStore = useSystemStore()
    const userInfo = systemStore.userInfo
    const refreshToken = userInfo?.[SystemStoreUserInfoEnum.REFRESH_TOKEN]

    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    console.log('🔄 开始刷新token...')

    try {
      // 调用CCAPI的token刷新接口
      const response = await fetch('http://localhost:3000/api/user/v2/token/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refreshToken: refreshToken
        })
      })

      if (!response.ok) {
        throw new Error(`Token refresh failed: ${response.status}`)
      }

      const data = await response.json()
      
      if (data.code !== 0) {
        throw new Error(data.msg || 'Token refresh failed')
      }

      const { token, refreshToken: newRefreshToken, expiresIn } = data.data

      // 更新存储的token信息
      systemStore.setItem(SystemStoreEnum.USER_INFO, {
        ...userInfo,
        [SystemStoreUserInfoEnum.USER_TOKEN]: token,
        [SystemStoreUserInfoEnum.REFRESH_TOKEN]: newRefreshToken,
        [SystemStoreUserInfoEnum.EXPIRES_IN]: expiresIn.toString(),
      })

      console.log('✅ Token刷新成功:', {
        newToken: token.substring(0, 10) + '...',
        newRefreshToken: newRefreshToken.substring(0, 10) + '...',
        newExpiresIn: expiresIn
      })
      
      return token

    } catch (error) {
      console.error('❌ Token刷新失败:', error)
      
      // 刷新失败，跳转到登录页面
      const currentUrl = window.location.href
      console.log('🔄 跳转到CCAPI登录页面...')
      window.location.href = `http://localhost:3000/lowcode/login?redirect=${encodeURIComponent(currentUrl)}`
      
      throw error
    }
  }

  // 检查token是否即将过期并刷新
  public async checkAndRefreshToken(): Promise<string | null> {
    const systemStore = useSystemStore()
    const userInfo = systemStore.userInfo
    const expiresIn = userInfo?.[SystemStoreUserInfoEnum.EXPIRES_IN]

    if (!expiresIn) return null

    const now = Math.floor(Date.now() / 1000)
    const expiresAt = parseInt(expiresIn)
    const timeUntilExpiry = expiresAt - now

    // 如果token在2分钟内过期，立即刷新
    if (timeUntilExpiry < 120 && !this.isRefreshing) {
      console.log('⚠️ Token即将过期，立即刷新...')
      try {
        return await this.refreshToken()
      } catch (error) {
        console.error('Token刷新失败:', error)
        return null
      }
    }

    return null
  }

  // 清理定时器
  destroy() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
      this.refreshTimer = null
    }
    console.log('🧹 Token刷新机制已清理')
  }
}

// 全局实例
export const tokenRefreshManager = new TokenRefreshManager()

// 初始化函数
export function initTokenRefreshMechanism() {
  tokenRefreshManager.init()
}

// 检查是否有有效的认证信息
export function hasValidAuthInfo(): boolean {
  const systemStore = useSystemStore()
  const userInfo = systemStore.userInfo

  return !!(
    userInfo?.[SystemStoreUserInfoEnum.USER_TOKEN] &&
    userInfo?.[SystemStoreUserInfoEnum.REFRESH_TOKEN]
  )
}

// 手动刷新token（供外部调用）
export async function manualRefreshToken(): Promise<string> {
  return tokenRefreshManager.refreshToken()
}
